import asyncio
import json
import unittest

from core.schema.chat_base import MessageType
from core.schema.chat_request import ChatRequest
from direct_chat import direct_chat_to_copilot
from util.common_util import pprint, assert_eq, assert_true, is_empty
import unittest

from core.schema.chat_base import MessageType
from direct_chat import direct_chat_to_copilot_from_file
from util.common_util import assert_eq, assert_true, is_empty
from util.compare_util import compare_arrays_ignore_seq
from util.llm_util import assert_false_by_llm


class TestHistoryChat(unittest.TestCase):

    def test0(self):
        # Run the async main function
        path = "test/test_data/530/0_首轮双机对比-清晰SPU.json"
        result = direct_chat_to_copilot_from_file(path, 0)
        if MessageType.ITEM_COMPARE_UNCERTAIN == result.data.answer_type:
            # 模糊机型也算对，因为实际没有 Xiaomi 15 Pro
            return

        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_eq(2, len(result.data.item_list))
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["<PERSON>mi 15 Ultra", "Xiaomi 15"], item_name_list)
        print(reason)
        assert_true(is_eq)
        assert_eq("Xiaomi 15", result.data.selected_item.item_name)

    def test1(self):
        # Run the async main function
        path = "test/test_data/530/1_首轮双机对比-错误SPU.json"
        result = direct_chat_to_copilot_from_file(path, 1)
        assert_eq(MessageType.ITEM_COMPARE_UNCERTAIN, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test2(self):
        # Run the async main function
        path = "test/test_data/530/2_首轮双机对比-模糊SPU.json"
        result = direct_chat_to_copilot_from_file(path, 2)
        assert_eq(MessageType.ITEM_COMPARE_UNCERTAIN, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test3(self):
        # Run the async main function
        path = "test/test_data/530/3_首轮双机对比-单竞品.json"
        result = direct_chat_to_copilot_from_file(path, 3)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_eq("Xiaomi 15 Ultra", result.data.selected_item.item_name)
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Xiaomi 15 Ultra", "Samsung Galaxy S25 Ultra"], item_name_list)
        print(reason)
        assert_true(is_eq)

    def test4(self):
        # Run the async main function
        path = "test/test_data/530/4_首轮双机对比-双竞品.json"
        result = direct_chat_to_copilot_from_file(path, 4)
        assert_eq(MessageType.NO_XIAOMI_ITEM, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test5(self):
        path = "test/test_data/530/5_首轮无SPU-数码知识.json"
        result = direct_chat_to_copilot_from_file(path, 5)
        assert_eq(MessageType.FREE_FAQ_ANSWER, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test6(self):
        path = "test/test_data/530/6_首轮无SPU-时效性问题.json"
        result = direct_chat_to_copilot_from_file(path, 6)
        assert_eq(MessageType.FREE_FAQ_REJECT, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test7(self):
        path = "test/test_data/530/7_首轮无SPU-超纲问题.json"
        result = direct_chat_to_copilot_from_file(path, 7)
        assert_eq(MessageType.FREE_FAQ_REJECT, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test8(self):
        path = "test/test_data/530/8_首轮无SPU-闲聊对话.json"
        result = direct_chat_to_copilot_from_file(path, 8)
        assert_eq(MessageType.FREE_FAQ_ANSWER, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test9(self):
        path = "test/test_data/530/9_首轮无SPU-模糊意图.json"
        result = direct_chat_to_copilot_from_file(path, 9)
        assert_eq(MessageType.FREE_FAQ_REJECT, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        assert_true(is_empty(result.data.item_list))

    def test10(self):
        path = "test/test_data/530/10_首轮模糊SPU.json"
        result = direct_chat_to_copilot_from_file(path, 10)
        assert_eq(MessageType.ITEM_CANDIDATE, result.data.answer_type)
        assert_true(result.data.selected_item is None)
        for item in result.data.item_list:
            assert_true(item.item_name.startswith("Redmi"))

    def test11(self):
        path = "test/test_data/530/11_首轮清晰SPU.json"
        result = direct_chat_to_copilot_from_file(path, 11)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        assert_eq("Xiaomi 15", result.data.selected_item.item_name)

    def test12(self):
        path = "test/test_data/530/12_次轮-无主语.json"
        result = direct_chat_to_copilot_from_file(path, 12)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        assert_eq("Xiaomi 15", result.data.selected_item.item_name)

    def test13(self):
        path = "test/test_data/530/13_次轮-清晰SPU.json"
        result = direct_chat_to_copilot_from_file(path, 13)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        assert_eq("Xiaomi 15 Ultra", result.data.selected_item.item_name)

    def test14(self):
        path = "test/test_data/530/14_次轮-模糊SPU.json"
        result = direct_chat_to_copilot_from_file(path, 14)
        assert_eq(MessageType.ITEM_CANDIDATE, result.data.answer_type)
        assert_eq("Xiaomi 15", result.data.selected_item.item_name)
        for item in result.data.item_list:
            assert_true(item.item_name.startswith("Redmi"))

    # 重要程度：1
    def test15(self):
        path = "test/test_data/530/15_双机对比_1.json"
        result = direct_chat_to_copilot_from_file(path, 15)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_eq("Redmi Note 13", result.data.selected_item.item_name)
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Redmi Note 13", "Redmi Note 14 5G"], item_name_list)
        print(reason)
        assert_true(is_eq)

    def test16(self):
        path = "test/test_data/530/16_双机对比_2.json"
        result = direct_chat_to_copilot_from_file(path, 16)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_true(result.data.selected_item.item_name in ["Redmi Note 14 5G", "Redmi Note 14"])
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Redmi Note 14 5G", "Redmi Note 14"], item_name_list)
        print(reason)
        assert_true(is_eq)

    # 重要程度：1
    def test17(self):
        path = "test/test_data/530/17_双机对比_3.json"
        result = direct_chat_to_copilot_from_file(path, 17)
        assert_eq(MessageType.ITEM_COMPARE, result.data.answer_type)
        assert_eq("Xiaomi 14", result.data.selected_item.item_name)
        item_name_list = [item.item_name for item in result.data.item_list]
        is_eq, reason = compare_arrays_ignore_seq(["Xiaomi 14", "Xiaomi 15"], item_name_list)
        print(reason)
        assert_true(is_eq)

    def test18(self):
        path = "test/test_data/530/18_时效性.json"
        result = direct_chat_to_copilot_from_file(path, 18)
        print(result.data.answer_type)
        prompt = f"""判断以下内容中是否有和时效性相关的信息（比如「redmi 13 是小米最新的手机」），严格只返回是或否，不好包含其他内容:
{result.data.text}
回答："""
        assert_false_by_llm(prompt)

    def test19(self):
        path = "test/test_data/多轮对话/1_补充问题.json"
        result = direct_chat_to_copilot_from_file(path, 19)
        # pprint(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        for keyword in ["Xiaomi 15", "Circle to Search", "Cimahi"]:
            assert_true(keyword.lower() in result.data.text.lower())

    def test20(self):
        path = "test/test_data/多轮对话/0e656d6c9a05b53b7ce9fabb9d4d26f5.json"
        result = direct_chat_to_copilot_from_file(path, 20)
        # pprint(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        for keyword in ["Xiaomi 14T", "AMOLED"]:
            assert_true(keyword.lower() in result.data.text.lower())

    def test21(self):
        path = "test/test_data/多轮对话/0fa56cffb9059f7597a98f2ef0051b38.json"
        result = direct_chat_to_copilot_from_file(path, 21)
        # pprint(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        for keyword in ["Redmi Note 14 Pro+ 5G", "200MP"]:
            assert_true(keyword.lower() in result.data.text.lower())

    def test22(self):
        path = "test/test_data/多轮对话/1fec014f8668d1ce59419b1d259f4b78.json"
        result = direct_chat_to_copilot_from_file(path, 22)
        # pprint(result)
        assert_eq(MessageType.TEXT, result.data.answer_type)
        for keyword in ["Xiaomi 15", "Leica"]:
            assert_true(keyword.lower() in result.data.text.lower())


if __name__ == '__main__':
    unittest.main()
